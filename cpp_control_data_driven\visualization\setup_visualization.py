#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化环境设置脚本
用于检查和安装Python可视化依赖
"""

import subprocess
import sys
import os
import importlib.util

def check_package(package_name):
    """检查包是否已安装"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def install_package(package_name):
    """安装包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def setup_visualization_environment():
    """设置可视化环境"""
    print("=== MPC Visualization Environment Setup ===")
    
    # 必需的包
    required_packages = [
        'numpy',
        'pandas', 
        'matplotlib',
        'scipy'
    ]
    
    # 可选的包
    optional_packages = [
        'seaborn',
        'plotly',
        'pillow',
        'imageio',
        'imageio-ffmpeg'
    ]
    
    print("\n1. Checking required packages...")
    missing_required = []
    for package in required_packages:
        if check_package(package):
            print(f"✓ {package} is installed")
        else:
            print(f"✗ {package} is missing")
            missing_required.append(package)
    
    print("\n2. Checking optional packages...")
    missing_optional = []
    for package in optional_packages:
        if check_package(package):
            print(f"✓ {package} is installed")
        else:
            print(f"✗ {package} is missing (optional)")
            missing_optional.append(package)
    
    # 安装缺失的必需包
    if missing_required:
        print(f"\n3. Installing missing required packages: {missing_required}")
        for package in missing_required:
            print(f"Installing {package}...")
            if install_package(package):
                print(f"✓ {package} installed successfully")
            else:
                print(f"✗ Failed to install {package}")
                return False
    
    # 询问是否安装可选包
    if missing_optional:
        print(f"\n4. Optional packages missing: {missing_optional}")
        response = input("Install optional packages? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            for package in missing_optional:
                print(f"Installing {package}...")
                if install_package(package):
                    print(f"✓ {package} installed successfully")
                else:
                    print(f"✗ Failed to install {package} (optional)")
    
    print("\n5. Creating output directories...")
    os.makedirs("../fig", exist_ok=True)
    os.makedirs("../fig/frames", exist_ok=True)
    print("✓ Output directories created")
    
    print("\n=== Setup Complete ===")
    print("You can now run the visualization scripts:")
    print("  python plot_modeling_results.py --data <data.csv> --model <model_dir>")
    print("  python plot_mpc_results.py --results <results.csv>")
    print("  python create_mpc_animation.py --results <results.csv>")
    
    return True

def test_visualization():
    """测试可视化功能"""
    print("\n=== Testing Visualization ===")
    
    try:
        import numpy as np
        import matplotlib.pyplot as plt
        
        # 创建测试图
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        
        plt.figure(figsize=(8, 6))
        plt.plot(x, y, 'b-', linewidth=2, label='sin(x)')
        plt.xlabel('x')
        plt.ylabel('y')
        plt.title('Visualization Test')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 保存测试图
        test_file = "../fig/visualization_test.png"
        plt.savefig(test_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Test plot saved to: {test_file}")
        return True
        
    except Exception as e:
        print(f"✗ Visualization test failed: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == '--test':
        # 只运行测试
        test_visualization()
    else:
        # 完整设置
        if setup_visualization_environment():
            test_visualization()

if __name__ == "__main__":
    main()
