#include <iostream>
#include <vector>
#include <string>
#include <chrono>
#include <fstream>
#include <iomanip>
#include <cstdlib>
#include <Eigen/Dense>
#include <qpOASES.hpp>

#include "data_loader.h"
#include "state_space_model.h"
#include "mpc_controller.h"
#include "matlab_style_plot.h"

// 保存结果到CSV文件
void saveResultsToCSV(const std::string& filename,
                     const std::vector<double>& time,
                     const std::vector<Eigen::VectorXd>& y_hist,
                     const std::vector<Eigen::VectorXd>& y_ref,
                     const std::vector<Eigen::VectorXd>& u_hist,
                     const std::vector<Eigen::VectorXd>& u_original,
                     const std::vector<int>& qp_status);

int main(int /*argc*/, char* /*argv*/[]) {
    // 在程序开始时就禁用qpOASES的所有输出
    qpOASES::getGlobalMessageHandler()->setErrorVisibilityStatus(qpOASES::VS_HIDDEN);
    qpOASES::getGlobalMessageHandler()->setWarningVisibilityStatus(qpOASES::VS_HIDDEN);
    qpOASES::getGlobalMessageHandler()->setInfoVisibilityStatus(qpOASES::VS_HIDDEN);

    std::cout << "MPC Control Program" << std::endl;
    
    // 1. 配置参数
    std::string dbFilename = "../data/measure_20250601.sqlite";
    std::string modelDir = "../models/ss_ab_model_cpp";  // Use trained model
    
    int predictionHorizon = 60;
    int controlHorizon = 30;
    
    double outputWeight = 100.0;
    double inputWeight = 0.01;
    double inputRateWeight = 1.0;
    
    double u_min = 0.0;  // 最小功率
    double u_max = 100.0; // 最大功率
    
    double du_min = -10.0; // 最小功率变化率
    double du_max = 10.0;  // 最大功率变化率
    
    int start = 1;
    int timeStep = 2000;  // 大规模测试
    
    // 2. 加载数据
    DataLoader dataLoader;
    if (!dataLoader.loadFromDB(dbFilename)) {
        std::cerr << "Data loading failed" << std::endl;
        return 1;
    }
    
    // 3. 加载状态空间模型
    StateSpaceModel model;
    if (!model.loadModel(modelDir)) {
        std::cerr << "模型加载失败" << std::endl;
        return 1;
    }
    
    // 获取模型中定义的状态、输入和输出列
    std::vector<std::string> stateCols = model.getStateColumns();
    std::vector<std::string> inputCols = model.getInputColumns();
    std::vector<std::string> outputCols = model.getOutputColumns();
    
    // 设置数据列
    dataLoader.setStateColumns(stateCols);
    dataLoader.setInputColumns(inputCols);
    if (!outputCols.empty()) {
        dataLoader.setOutputColumn(outputCols[0]);
    }
    
    // 4. 准备MPC控制器
    MPCController mpcController;
    mpcController.setModel(model);
    mpcController.setPredictionHorizon(predictionHorizon);
    mpcController.setControlHorizon(controlHorizon);
    
    // 设置权重
    mpcController.setOutputWeight(outputWeight);
    mpcController.setInputWeight(inputWeight);
    mpcController.setInputRateWeight(inputRateWeight);
    
    // 设置约束
    mpcController.setInputConstraints(u_min, u_max);
    mpcController.setInputRateConstraints(du_min, du_max);
    
    // 定义参考温度
    // double y_min = 0.0;  // 输出约束会从参考值推导 - Unused
    // double y_max = 0.0;  // Unused
    
    // 5. 提取所需数据
    // 确保数据足够长
    int totalRows = static_cast<int>(dataLoader.getRowCount());
    std::cout << "Total data rows: " << totalRows << std::endl;
    std::cout << "Requested start: " << start << ", timeStep: " << timeStep << ", horizon: " << predictionHorizon << std::endl;

    if (totalRows < start + timeStep + predictionHorizon) {
        timeStep = std::max(1, totalRows - start - predictionHorizon);
        std::cout << "Warning: Insufficient data, simulation steps adjusted to " << timeStep << std::endl;
    }

    if (timeStep <= 0) {
        std::cerr << "Error: Not enough data for simulation!" << std::endl;
        return 1;
    }
    
    // 获取输入、状态和参考数据（使用全部数据，不只是训练数据）
    Eigen::MatrixXd X_data = dataLoader.getAllStateMatrix();  // 获取全部数据
    Eigen::MatrixXd U_data = dataLoader.getAllInputMatrix();  // 获取全部数据
    Eigen::VectorXd Y_ref = dataLoader.getAllReferenceOutput();  // 获取全部数据
    
    // 6. 运行MPC模拟
    std::cout << "Starting MPC simulation..." << std::endl;
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // 历史记录
    std::vector<Eigen::VectorXd> u_hist;
    std::vector<Eigen::VectorXd> y_hist;
    std::vector<Eigen::VectorXd> u_original;
    std::vector<Eigen::VectorXd> y_ref_hist;
    std::vector<int> qp_status_hist;
    std::vector<double> time_hist;
    
    // 初始化控制输入和状态
    std::cout << "Initializing control and state..." << std::endl;

    std::cout << "Creating initial control input..." << std::endl;
    Eigen::VectorXd u_prev = Eigen::VectorXd::Zero(1);

    std::cout << "Accessing U_data at (" << start << ", 0)..." << std::endl;
    std::cout << "U_data dimensions: [" << U_data.rows() << ", " << U_data.cols() << "]" << std::endl;

    if (start >= U_data.rows()) {
        std::cerr << "Error: start index " << start << " >= U_data.rows() " << U_data.rows() << std::endl;
        return 1;
    }

    u_prev(0) = U_data(start, 0);
    std::cout << "Initial power: " << u_prev(0) << std::endl;

    std::cout << "Creating initial state..." << std::endl;
    std::cout << "X_data dimensions: [" << X_data.rows() << ", " << X_data.cols() << "]" << std::endl;

    if (start >= X_data.rows()) {
        std::cerr << "Error: start index " << start << " >= X_data.rows() " << X_data.rows() << std::endl;
        return 1;
    }

    Eigen::VectorXd currentState = X_data.row(start);
    Eigen::VectorXd currentOutput(1);
    currentOutput(0) = currentState(currentState.size() - 1);  // 最后一个状态是出口温度
    std::cout << "Initial exit temperature: " << currentOutput(0) << " C" << std::endl;
    
    std::cout << "Starting MPC simulation with " << timeStep << " steps..." << std::endl;
    std::cout << "Initial state size: " << X_data.cols() << std::endl;
    std::cout << "Input data size: " << U_data.cols() << std::endl;
    std::cout << "Reference size: " << Y_ref.size() << std::endl;
    std::cout << "Progress: ";

    for (int k = 0; k < timeStep; ++k) {
        // 显示进度
        if (k % std::max(1, timeStep / 10) == 0 || k == timeStep - 1) {
            std::cout << "[" << (k * 100 / timeStep) << "%] ";
            std::cout.flush();
        }

        // 添加边界检查
        if (k + start >= totalRows) {
            std::cout << "\nWarning: Reached end of data at step " << k << std::endl;
            timeStep = k;
            break;
        }
        // 设置参考轨迹和扰动
        if (k == 0) std::cout << "\nSetting up reference trajectory..." << std::endl;

        Eigen::VectorXd ref(predictionHorizon);
        for (int i = 0; i < predictionHorizon; ++i) {
            if (k + i + start < Y_ref.size()) {
                ref(i) = Y_ref(k + i + start);
            } else {
                ref(i) = Y_ref(Y_ref.size() - 1);  // 使用最后一个值
            }
        }
        mpcController.setReference(ref);

        if (k == 0) std::cout << "Reference set, first value: " << ref(0) << " C" << std::endl;
        
        // 设置测量扰动（除了功率以外的其他输入）
        if (k == 0) std::cout << "Setting up disturbances..." << std::endl;

        Eigen::MatrixXd disturbances(predictionHorizon, U_data.cols() - 1);
        for (int i = 0; i < predictionHorizon; ++i) {
            if (k + i + start < U_data.rows()) {
                disturbances.row(i) = U_data.row(k + i + start).segment(1, U_data.cols() - 1);
            } else {
                disturbances.row(i) = U_data.row(U_data.rows() - 1).segment(1, U_data.cols() - 1);
            }
        }
        mpcController.setMeasuredDisturbances(disturbances);

        if (k == 0) std::cout << "Disturbances set, calling MPC step..." << std::endl;
        
        // 计算控制输入
        Eigen::VectorXd u_opt;
        try {
            u_opt = mpcController.step(currentState, currentOutput);
        } catch (const std::exception& e) {
            std::cerr << "\nException in MPC step " << k << ": " << e.what() << std::endl;
            return 1;
        } catch (...) {
            std::cerr << "\nUnknown exception in MPC step " << k << std::endl;
            return 1;
        }
        
        // 提取原始输入数据
        Eigen::VectorXd u_orig(U_data.cols());
        u_orig = U_data.row(k + start);
        
        // 创建带MPC功率的完整输入向量
        Eigen::VectorXd u_full = U_data.row(k + start);
        u_full(0) = u_opt(0);  // 替换功率
        
        // 模拟系统响应
        const Eigen::MatrixXd& A = model.getA();
        const Eigen::MatrixXd& B = model.getB();
        const Eigen::MatrixXd& C = model.getC();
        
        Eigen::VectorXd nextState = A * currentState + B * u_full;
        Eigen::VectorXd nextOutput = C * nextState;
        
        // 更新状态和输出
        currentState = nextState;
        currentOutput = nextOutput;
        
        // 记录历史
        u_hist.push_back(u_opt);
        y_hist.push_back(currentOutput);
        u_original.push_back(u_orig);
        
        Eigen::VectorXd ref_k(1);
        ref_k(0) = Y_ref(k + start);
        y_ref_hist.push_back(ref_k);
        
        qp_status_hist.push_back(mpcController.getQPStatus());
        time_hist.push_back(k * 5.0); // 每个步长5秒
        
        // 调试输出
        if (k % 10 == 0) {
            std::cout << "Step " << k
                     << ", Ref temp = " << ref_k(0)
                     << ", Pred temp = " << currentOutput(0)
                     << ", Power = " << u_opt(0)
                     << ", QP status = " << mpcController.getQPStatus()
                     << std::endl;
        }
    }
    
    std::cout << " [100%] Complete!" << std::endl;

    auto endTime = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double> elapsed = endTime - startTime;
    std::cout << "\nMPC simulation completed!" << std::endl;
    std::cout << "Total simulation time: " << elapsed.count() << " seconds" << std::endl;
    std::cout << "Average time per step: " << (elapsed.count() / timeStep) * 1000 << " ms" << std::endl;
    
    // 7. 计算性能指标
    // 计算误差
    double mse = 0.0;
    double rmse = 0.0;
    double mae = 0.0;
    double maxDeviation = 0.0;
    
    for (size_t i = 0; i < y_hist.size(); ++i) {
        double error = y_ref_hist[i](0) - y_hist[i](0);
        mse += error * error;
        mae += std::abs(error);
        maxDeviation = std::max(maxDeviation, std::abs(error));
    }
    
    mse /= y_hist.size();
    rmse = std::sqrt(mse);
    mae /= y_hist.size();

    // 计算QP求解成功率
    int successful_qp = 0;
    for (int status : qp_status_hist) {
        if (status == 0) successful_qp++;
    }
    double success_rate = (double)successful_qp / qp_status_hist.size() * 100.0;

    // 计算控制输入统计
    double avg_power = 0.0;
    double max_power = -1e6;
    double min_power = 1e6;
    double power_std = 0.0;

    for (const auto& u : u_hist) {
        avg_power += u(0);
        max_power = std::max(max_power, u(0));
        min_power = std::min(min_power, u(0));
    }
    avg_power /= u_hist.size();

    // 计算功率标准差
    for (const auto& u : u_hist) {
        power_std += (u(0) - avg_power) * (u(0) - avg_power);
    }
    power_std = std::sqrt(power_std / u_hist.size());

    // 显示详细统计
    std::cout << "\n=== MPC Control Performance Statistics ===" << std::endl;
    std::cout << "Simulation Steps: " << timeStep << std::endl;
    std::cout << "Prediction Horizon: " << predictionHorizon << std::endl;
    std::cout << "Control Horizon: " << controlHorizon << std::endl;
    std::cout << "\n--- Temperature Control Performance ---" << std::endl;
    std::cout << "RMSE: " << rmse << " C" << std::endl;
    std::cout << "MAE:  " << mae << " C" << std::endl;
    std::cout << "MSE:  " << mse << " C^2" << std::endl;
    std::cout << "Max Deviation: " << maxDeviation << " C" << std::endl;
    std::cout << "\n--- Power Control Statistics ---" << std::endl;
    std::cout << "Average Power: " << avg_power << " %" << std::endl;
    std::cout << "Power Range: [" << min_power << ", " << max_power << "] %" << std::endl;
    std::cout << "Power Std Dev: " << power_std << " %" << std::endl;
    std::cout << "\n--- QP Solver Performance ---" << std::endl;
    std::cout << "Success Rate: " << success_rate << " %" << std::endl;
    std::cout << "Successful Solves: " << successful_qp << "/" << qp_status_hist.size() << std::endl;
    
    // 8. Save results
    std::string resultsFile = "../results/mpc_control_results_ph" +
                             std::to_string(predictionHorizon) +
                             "_ch" + std::to_string(controlHorizon) + ".csv";
    
    saveResultsToCSV(resultsFile, time_hist, y_hist, y_ref_hist, u_hist, u_original, qp_status_hist);
    std::cout << "\nResults saved to: " << resultsFile << std::endl;

    // 生成MATLAB风格的图表
    std::cout << "\nGenerating MATLAB-style plots..." << std::endl;

    // 准备绘图数据
    std::vector<double> time_steps;
    std::vector<double> reference_vec, measured_vec, predicted_vec;
    std::vector<std::vector<double>> control_inputs_data;
    std::vector<std::vector<double>> states_data;

    // 转换时间步数据
    for (size_t i = 0; i < time_hist.size(); ++i) {
        time_steps.push_back(static_cast<double>(i + 1)); // 时间步从1开始
    }

    // 转换输出数据
    for (size_t i = 0; i < y_hist.size(); ++i) {
        predicted_vec.push_back(y_hist[i](0)); // MPC预测输出
        reference_vec.push_back(y_ref_hist[i](0)); // 参考值
        // 使用参考值作为实际测量值（在仿真中）
        measured_vec.push_back(y_ref_hist[i](0)); // 实际测量值
    }

    // 转换控制输入数据
    control_inputs_data.resize(u_hist[0].size());
    for (int j = 0; j < u_hist[0].size(); ++j) {
        for (size_t i = 0; i < u_hist.size(); ++i) {
            control_inputs_data[j].push_back(u_hist[i](j));
        }
    }

    // 添加原始控制输入数据（如果需要对比）
    if (!u_original.empty()) {
        control_inputs_data.resize(control_inputs_data.size() + u_original[0].size());
        for (int j = 0; j < u_original[0].size(); ++j) {
            int idx = u_hist[0].size() + j;
            for (size_t i = 0; i < u_original.size(); ++i) {
                control_inputs_data[idx].push_back(u_original[i](j));
            }
        }
    }

    // 转换状态数据（如果有状态历史记录）
    // 这里假设我们有状态历史，如果没有可以留空
    states_data.resize(stateCols.size());
    for (size_t j = 0; j < stateCols.size(); ++j) {
        for (size_t i = 0; i < time_hist.size(); ++i) {
            // 这里需要从实际的状态历史中获取数据
            // 暂时用预测输出填充，实际应用中需要修改
            states_data[j].push_back(predicted_vec[i]);
        }
    }

    // 调用绘图函数
    plotMPCResults(time_steps, reference_vec, measured_vec, predicted_vec,
                   control_inputs_data, states_data, inputCols, stateCols,
                   predictionHorizon, controlHorizon, "../fig");

    std::cout << "MATLAB-style plots generated successfully!" << std::endl;
    std::cout << "Check the ./fig directory for generated plots" << std::endl;

    return 0;
}

void saveResultsToCSV(const std::string& filename,
                     const std::vector<double>& time,
                     const std::vector<Eigen::VectorXd>& y_hist,
                     const std::vector<Eigen::VectorXd>& y_ref,
                     const std::vector<Eigen::VectorXd>& u_hist,
                     const std::vector<Eigen::VectorXd>& u_original,
                     const std::vector<int>& qp_status) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法创建结果文件: " << filename << std::endl;
        return;
    }
    
    // 写入列头
    file << "time,y_mpc,y_ref,y_error,u_mpc,u_original,qp_status\n";
    
    // 写入数据行
    for (size_t i = 0; i < time.size(); ++i) {
        file << std::fixed << std::setprecision(2);
        file << time[i] << ",";
        file << y_hist[i](0) << ",";
        file << y_ref[i](0) << ",";
        file << (y_ref[i](0) - y_hist[i](0)) << ",";
        file << u_hist[i](0) << ",";
        file << u_original[i](0) << ",";
        file << qp_status[i] << "\n";
    }
    
    file.close();
} 