#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MPC优化过程动画生成脚本
用于生成MPC控制过程的动画，展示预测视界和控制效果
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Rectangle
import argparse
import os
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MPCAnimator:
    def __init__(self, results_file, prediction_horizon=60, control_horizon=30):
        """初始化MPC动画器"""
        self.results = pd.read_csv(results_file)
        self.Np = prediction_horizon
        self.Nc = control_horizon
        
        # 提取数据
        self.time_steps = np.arange(len(self.results))
        self.y_mpc = self.results['y_mpc'].values
        self.y_ref = self.results['y_ref'].values
        self.u_mpc = self.results['u_mpc'].values
        self.y_error = self.results['y_error'].values
        
        # 设置动画参数
        self.current_step = 0
        self.max_steps = len(self.results)
        
        # 创建图形
        self.fig, self.axes = plt.subplots(2, 2, figsize=(16, 10))
        self.fig.suptitle('MPC Real-time Control Animation', fontsize=16, fontweight='bold')
        
        # 初始化子图
        self.setup_plots()
        
    def setup_plots(self):
        """设置子图"""
        # 1. 温度控制性能（左上）
        self.ax1 = self.axes[0, 0]
        self.ax1.set_title('Temperature Control with Prediction Horizon')
        self.ax1.set_xlabel('Time Step')
        self.ax1.set_ylabel('Temperature (°C)')
        self.ax1.grid(True, alpha=0.3)
        
        # 2. 控制输入（右上）
        self.ax2 = self.axes[0, 1]
        self.ax2.set_title('Power Control with Control Horizon')
        self.ax2.set_xlabel('Time Step')
        self.ax2.set_ylabel('Power (%)')
        self.ax2.grid(True, alpha=0.3)
        
        # 3. 控制误差（左下）
        self.ax3 = self.axes[1, 0]
        self.ax3.set_title('Control Error Evolution')
        self.ax3.set_xlabel('Time Step')
        self.ax3.set_ylabel('Error (°C)')
        self.ax3.grid(True, alpha=0.3)
        
        # 4. 实时性能指标（右下）
        self.ax4 = self.axes[1, 1]
        self.ax4.set_title('Real-time Performance Metrics')
        self.ax4.axis('off')
        
        # 初始化线条和文本
        self.lines = {}
        self.texts = {}
        self.patches = {}
        
    def animate_frame(self, frame):
        """动画帧更新函数"""
        self.current_step = frame
        
        # 清除之前的图形
        for ax in self.axes.flat:
            ax.clear()
        
        # 重新设置子图
        self.setup_plots()
        
        # 计算显示范围
        window_size = min(100, self.max_steps)  # 显示窗口大小
        start_idx = max(0, self.current_step - window_size // 2)
        end_idx = min(self.max_steps, start_idx + window_size)
        
        # 调整start_idx以确保窗口大小一致
        if end_idx - start_idx < window_size and start_idx > 0:
            start_idx = max(0, end_idx - window_size)
        
        display_steps = np.arange(start_idx, end_idx)
        
        # 1. 温度控制性能图
        if len(display_steps) > 0:
            # 历史数据
            hist_steps = display_steps[display_steps <= self.current_step]
            if len(hist_steps) > 0:
                self.ax1.plot(hist_steps, self.y_mpc[hist_steps], 'b-', linewidth=2, label='MPC Output')
                self.ax1.plot(hist_steps, self.y_ref[hist_steps], 'r--', linewidth=1.5, label='Reference')
            
            # 当前点
            if self.current_step < self.max_steps:
                self.ax1.plot(self.current_step, self.y_mpc[self.current_step], 'bo', markersize=8, label='Current')
                
                # 预测视界
                future_steps = np.arange(self.current_step, min(self.current_step + self.Np, self.max_steps))
                if len(future_steps) > 1:
                    # 假设预测值（这里用实际值代替，实际应用中应该是预测值）
                    pred_y = self.y_ref[future_steps]  # 简化：假设预测跟踪参考值
                    self.ax1.plot(future_steps, pred_y, 'g:', linewidth=2, alpha=0.7, label=f'Prediction (Np={self.Np})')
                    
                    # 预测视界背景
                    self.ax1.axvspan(self.current_step, future_steps[-1], alpha=0.1, color='green')
        
        self.ax1.legend()
        self.ax1.set_xlim(start_idx, end_idx)
        
        # 2. 控制输入图
        if len(display_steps) > 0:
            # 历史数据
            hist_steps = display_steps[display_steps <= self.current_step]
            if len(hist_steps) > 0:
                self.ax2.plot(hist_steps, self.u_mpc[hist_steps], 'b-', linewidth=2, label='MPC Power')
            
            # 当前点
            if self.current_step < self.max_steps:
                self.ax2.plot(self.current_step, self.u_mpc[self.current_step], 'bo', markersize=8, label='Current')
                
                # 控制视界
                control_steps = np.arange(self.current_step, min(self.current_step + self.Nc, self.max_steps))
                if len(control_steps) > 1:
                    # 控制视界背景
                    self.ax2.axvspan(self.current_step, control_steps[-1], alpha=0.2, color='orange', label=f'Control Horizon (Nc={self.Nc})')
        
        self.ax2.legend()
        self.ax2.set_xlim(start_idx, end_idx)
        self.ax2.set_ylim(0, 100)
        
        # 3. 控制误差图
        if len(display_steps) > 0:
            hist_steps = display_steps[display_steps <= self.current_step]
            if len(hist_steps) > 0:
                self.ax3.plot(hist_steps, self.y_error[hist_steps], 'r-', linewidth=2, label='Control Error')
                self.ax3.axhline(y=0, color='k', linestyle='--', alpha=0.5)
            
            # 当前点
            if self.current_step < self.max_steps:
                self.ax3.plot(self.current_step, self.y_error[self.current_step], 'ro', markersize=8, label='Current Error')
        
        self.ax3.legend()
        self.ax3.set_xlim(start_idx, end_idx)
        
        # 4. 实时性能指标
        if self.current_step > 0:
            # 计算到当前步的性能指标
            current_errors = self.y_error[:self.current_step+1]
            current_rmse = np.sqrt(np.mean(current_errors**2))
            current_mae = np.mean(np.abs(current_errors))
            current_max_error = np.max(np.abs(current_errors))
            
            # 当前状态
            current_temp = self.y_mpc[self.current_step] if self.current_step < self.max_steps else 0
            current_ref = self.y_ref[self.current_step] if self.current_step < self.max_steps else 0
            current_power = self.u_mpc[self.current_step] if self.current_step < self.max_steps else 0
            current_error = self.y_error[self.current_step] if self.current_step < self.max_steps else 0
            
            metrics_text = f"""Current Step: {self.current_step + 1}/{self.max_steps}
Progress: {(self.current_step + 1)/self.max_steps*100:.1f}%

Current Status:
Temperature: {current_temp:.2f}°C
Reference: {current_ref:.2f}°C
Power: {current_power:.2f}%
Error: {current_error:.2f}°C

Cumulative Performance:
RMSE: {current_rmse:.4f}°C
MAE: {current_mae:.4f}°C
Max Error: {current_max_error:.2f}°C

MPC Parameters:
Prediction Horizon: {self.Np}
Control Horizon: {self.Nc}"""
            
            self.ax4.text(0.05, 0.95, metrics_text, transform=self.ax4.transAxes,
                         verticalalignment='top', fontfamily='monospace', fontsize=10,
                         bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        # 添加进度条
        progress = (self.current_step + 1) / self.max_steps
        progress_rect = Rectangle((0.05, 0.02), 0.9 * progress, 0.03, 
                                transform=self.ax4.transAxes, facecolor='green', alpha=0.7)
        self.ax4.add_patch(progress_rect)
        
        plt.tight_layout()
        
        return []
    
    def create_animation(self, output_file='mpc_animation.gif', fps=5, skip_frames=1):
        """创建动画"""
        print(f"Creating animation with {self.max_steps} frames...")
        
        # 创建动画，跳过一些帧以减少文件大小
        frames = range(0, self.max_steps, skip_frames)
        
        anim = animation.FuncAnimation(
            self.fig, self.animate_frame, frames=frames,
            interval=1000//fps, blit=False, repeat=True
        )
        
        # 保存动画
        print(f"Saving animation to {output_file}...")
        if output_file.endswith('.gif'):
            anim.save(output_file, writer='pillow', fps=fps, dpi=100)
        elif output_file.endswith('.mp4'):
            anim.save(output_file, writer='ffmpeg', fps=fps, dpi=100)
        else:
            # 默认保存为gif
            output_file += '.gif'
            anim.save(output_file, writer='pillow', fps=fps, dpi=100)
        
        print(f"Animation saved successfully!")
        return anim

def create_static_frames(results_file, output_dir='./frames', num_frames=10):
    """创建静态关键帧"""
    os.makedirs(output_dir, exist_ok=True)
    
    animator = MPCAnimator(results_file)
    
    # 选择关键帧
    frame_indices = np.linspace(0, animator.max_steps-1, num_frames, dtype=int)
    
    for i, frame_idx in enumerate(frame_indices):
        animator.animate_frame(frame_idx)
        
        output_file = os.path.join(output_dir, f'mpc_frame_{i+1:02d}_step_{frame_idx:04d}.png')
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        print(f"Saved frame {i+1}/{num_frames}: {output_file}")
    
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='Create MPC control animation')
    parser.add_argument('--results', required=True, help='Path to MPC results CSV file')
    parser.add_argument('--output', default='./fig/mpc_animation.gif', help='Output animation file')
    parser.add_argument('--fps', type=int, default=5, help='Frames per second')
    parser.add_argument('--skip', type=int, default=1, help='Skip frames to reduce file size')
    parser.add_argument('--frames-only', action='store_true', help='Generate static frames only')
    parser.add_argument('--num-frames', type=int, default=10, help='Number of static frames to generate')
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    output_dir = os.path.dirname(args.output)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    if args.frames_only:
        # 只生成静态帧
        frames_dir = os.path.join(output_dir, 'frames')
        create_static_frames(args.results, frames_dir, args.num_frames)
    else:
        # 创建完整动画
        animator = MPCAnimator(args.results)
        anim = animator.create_animation(args.output, args.fps, args.skip)
        
        # 也生成一些关键帧
        frames_dir = os.path.join(output_dir, 'frames')
        create_static_frames(args.results, frames_dir, 5)

if __name__ == "__main__":
    main()
