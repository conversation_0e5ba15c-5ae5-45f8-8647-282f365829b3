#pragma once

#include <Eigen/Dense>
#include "state_space_model.h"
#include <qpOASES.hpp>

class MPCController {
public:
    MPCController();
    ~MPCController();
    
    // 设置状态空间模型
    void setModel(const StateSpaceModel& model);
    
    // 设置MPC参数
    void setPredictionHorizon(int horizon);
    void setControlHorizon(int horizon);
    void setSamplingTime(double Ts);
    
    // 设置权重
    void setOutputWeight(double weight);
    void setInputWeight(double weight);
    void setInputRateWeight(double weight);
    
    // 设置约束
    void setInputConstraints(double min, double max);
    void setInputRateConstraints(double min, double max);
    void setOutputConstraints(double min, double max);
    
    // 设置参考轨迹和测量扰动
    void setReference(const Eigen::VectorXd& reference);
    void setMeasuredDisturbances(const Eigen::MatrixXd& disturbances);
    
    // MPC计算
    Eigen::VectorXd step(const Eigen::VectorXd& currentState, const Eigen::VectorXd& currentOutput);
    
    // 获取优化结果
    const Eigen::VectorXd& getOptimalInputs() const;
    const Eigen::VectorXd& getOptimalOutputs() const;
    const Eigen::VectorXd& getOptimalStates() const;
    int getQPStatus() const;
    
private:
    // 模型参数
    StateSpaceModel model_;
    int nx_;  // 状态变量数量
    int nu_;  // 控制输入数量
    int ny_;  // 输出变量数量
    int nd_;  // 测量扰动数量
    double Ts_; // 采样时间
    
    // MPC参数
    int predictionHorizon_;
    int controlHorizon_;
    
    // 权重
    double outputWeight_;
    double inputWeight_;
    double inputRateWeight_;
    
    // 约束
    double inputMin_;
    double inputMax_;
    double inputRateMin_;
    double inputRateMax_;
    double outputMin_;
    double outputMax_;
    
    // 参考轨迹和扰动
    Eigen::VectorXd reference_;
    Eigen::MatrixXd disturbances_;
    
    // 上一步控制输入
    Eigen::VectorXd lastInput_;
    
    // 优化结果
    Eigen::VectorXd optimalInputs_;
    Eigen::VectorXd optimalOutputs_;
    Eigen::VectorXd optimalStates_;
    int qpStatus_;
    
    // 内部方法：构建MPC问题的QP矩阵
    void buildQPMatrices(const Eigen::VectorXd& currentState,
                        Eigen::MatrixXd& H,
                        Eigen::VectorXd& g,
                        Eigen::MatrixXd& A_constraint,
                        Eigen::VectorXd& lb,
                        Eigen::VectorXd& ub);
    
    // 求解QP问题
    bool solveQP(const Eigen::MatrixXd& H,
                const Eigen::VectorXd& g,
                const Eigen::MatrixXd& A_constraint,
                const Eigen::VectorXd& lb,
                const Eigen::VectorXd& ub,
                Eigen::VectorXd& solution);
    
    // qpOASES求解器
    qpOASES::SQProblem* qpSolver_;
}; 