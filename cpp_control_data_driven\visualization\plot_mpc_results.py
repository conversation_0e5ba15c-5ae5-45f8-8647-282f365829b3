#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MPC控制结果可视化脚本
用于生成与MATLAB版本相同的MPC控制结果图表
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import argparse
import os
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def plot_mpc_results(results_file, data_file=None, output_dir='./fig'):
    """绘制MPC控制结果"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载MPC结果数据
    print("Loading MPC results...")
    results = pd.read_csv(results_file)
    
    # 提取数据
    time_steps = np.arange(len(results))
    time_h = results['time'].values / 3600  # 转换为小时
    y_mpc = results['y_mpc'].values
    y_ref = results['y_ref'].values
    y_error = results['y_error'].values
    u_mpc = results['u_mpc'].values
    u_original = results['u_original'].values
    qp_status = results['qp_status'].values
    
    # 如果提供了原始数据文件，加载额外信息
    additional_data = None
    if data_file and os.path.exists(data_file):
        print("Loading additional data...")
        additional_data = pd.read_csv(data_file)
    
    # 创建图表
    fig = plt.figure(figsize=(15, 12))
    fig.suptitle('MPC Control Results', fontsize=16, fontweight='bold')
    
    # 1. 温度控制性能对比
    ax1 = plt.subplot(3, 2, 1)
    ax1.plot(time_steps, y_mpc, 'b-', linewidth=2, label='MPC Control')
    ax1.plot(time_steps, y_ref, 'r--', linewidth=1.5, label='Reference/Setpoint')
    
    # 添加参考温度边界（±15度）
    ax1.plot(time_steps, y_ref + 15, 'k--', linewidth=2, alpha=0.5, label='Reference Bounds')
    ax1.plot(time_steps, y_ref - 15, 'k--', linewidth=2, alpha=0.5)
    ax1.fill_between(time_steps, y_ref - 15, y_ref + 15, alpha=0.1, color='gray')
    
    ax1.set_title('Exit Temperature Control Performance')
    ax1.set_xlabel('Time Step')
    ax1.set_ylabel('Temperature (°C)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 控制误差
    ax2 = plt.subplot(3, 2, 2)
    ax2.plot(time_steps, y_error, 'b-', linewidth=2, label='MPC Error')
    ax2.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax2.set_title('Control Error (Reference - Output)')
    ax2.set_xlabel('Time Step')
    ax2.set_ylabel('Error (°C)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 控制输入 - 平均功率
    ax3 = plt.subplot(3, 2, 3)
    ax3.plot(time_steps, u_mpc, 'b-', linewidth=2, label='MPC Power Control')
    ax3.plot(time_steps, u_original, 'r--', linewidth=1.5, label='Original Power')
    ax3.set_title('Average Power Control')
    ax3.set_xlabel('Time Step')
    ax3.set_ylabel('Power (%)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 控制输入变化率
    ax4 = plt.subplot(3, 2, 4)
    if len(u_mpc) > 1:
        power_rate = np.diff(u_mpc)
        ax4.plot(time_steps[1:], power_rate, 'b-', linewidth=1.5, label='Power Rate of Change')
        ax4.axhline(y=10, color='r', linestyle='--', alpha=0.7, label='Upper Limit')
        ax4.axhline(y=-10, color='r', linestyle='--', alpha=0.7, label='Lower Limit')
    ax4.set_title('Control Rate of Change')
    ax4.set_xlabel('Time Step')
    ax4.set_ylabel('Rate of Change (%/step)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. QP求解状态
    ax5 = plt.subplot(3, 2, 5)
    # 创建状态颜色映射
    status_colors = []
    for status in qp_status:
        if status == 0:
            status_colors.append('green')  # 成功
        elif status == 1:
            status_colors.append('orange')  # 达到最大迭代次数
        else:
            status_colors.append('red')  # 失败
    
    ax5.scatter(time_steps, qp_status, c=status_colors, alpha=0.7, s=20)
    ax5.set_title('QP Solver Status')
    ax5.set_xlabel('Time Step')
    ax5.set_ylabel('Status Code')
    ax5.grid(True, alpha=0.3)
    
    # 添加状态说明
    ax5.text(0.02, 0.98, 'Green: Success\nOrange: Max Iter\nRed: Failed', 
             transform=ax5.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # 6. 性能统计
    ax6 = plt.subplot(3, 2, 6)
    
    # 计算性能指标
    mse = np.mean(y_error**2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(y_error))
    max_error = np.max(np.abs(y_error))
    
    # 成功率统计
    success_rate = np.sum(qp_status == 0) / len(qp_status) * 100
    
    # 显示性能指标
    metrics_text = f"""Performance Metrics:
MSE: {mse:.4f}
RMSE: {rmse:.4f}
MAE: {mae:.4f}
Max Error: {max_error:.2f}°C
QP Success Rate: {success_rate:.1f}%

Control Statistics:
Avg Power: {np.mean(u_mpc):.2f}%
Power Std: {np.std(u_mpc):.2f}%
Max Power Change: {np.max(np.abs(np.diff(u_mpc))):.2f}%"""
    
    ax6.text(0.05, 0.95, metrics_text, transform=ax6.transAxes, 
             verticalalignment='top', fontfamily='monospace', fontsize=10,
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    ax6.set_title('Performance Summary')
    ax6.axis('off')
    
    plt.tight_layout()
    
    # 保存图片
    output_file = os.path.join(output_dir, 'CPP_MPC_control_results.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"MPC results plot saved to: {output_file}")
    
    plt.show()
    
    # 打印性能总结
    print("\n=== MPC Control Performance Summary ===")
    print(f"RMSE: {rmse:.4f}°C")
    print(f"MAE: {mae:.4f}°C")
    print(f"Max Error: {max_error:.2f}°C")
    print(f"QP Success Rate: {success_rate:.1f}%")
    print(f"Average Power: {np.mean(u_mpc):.2f}%")
    
    return {
        'rmse': rmse,
        'mae': mae,
        'max_error': max_error,
        'success_rate': success_rate,
        'avg_power': np.mean(u_mpc)
    }

def plot_time_series_comparison(results_file, output_dir='./fig'):
    """绘制时间序列对比图"""
    results = pd.read_csv(results_file)
    
    fig, axes = plt.subplots(2, 1, figsize=(15, 10))
    
    time_h = results['time'].values / 3600
    
    # 上图：温度对比
    axes[0].plot(time_h, results['y_mpc'], 'b-', linewidth=2, label='MPC Output')
    axes[0].plot(time_h, results['y_ref'], 'r--', linewidth=1.5, label='Reference')
    axes[0].fill_between(time_h, results['y_ref'] - 15, results['y_ref'] + 15, 
                        alpha=0.2, color='red', label='±15°C Bounds')
    axes[0].set_ylabel('Temperature (°C)')
    axes[0].set_title('Temperature Control Performance')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 下图：功率控制
    axes[1].plot(time_h, results['u_mpc'], 'b-', linewidth=2, label='MPC Power')
    axes[1].plot(time_h, results['u_original'], 'g--', linewidth=1.5, label='Original Power')
    axes[1].set_xlabel('Time (h)')
    axes[1].set_ylabel('Power (%)')
    axes[1].set_title('Power Control')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    output_file = os.path.join(output_dir, 'CPP_MPC_time_series.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Time series plot saved to: {output_file}")
    
    plt.show()

def main():
    parser = argparse.ArgumentParser(description='Plot MPC control results')
    parser.add_argument('--results', required=True, help='Path to MPC results CSV file')
    parser.add_argument('--data', help='Path to original data CSV file (optional)')
    parser.add_argument('--output', default='./fig', help='Output directory for plots')
    parser.add_argument('--time-series', action='store_true', help='Also generate time series plot')
    
    args = parser.parse_args()
    
    # 绘制主要结果
    metrics = plot_mpc_results(args.results, args.data, args.output)
    
    # 绘制时间序列对比（如果请求）
    if args.time_series:
        plot_time_series_comparison(args.results, args.output)
    
    return metrics

if __name__ == "__main__":
    main()
