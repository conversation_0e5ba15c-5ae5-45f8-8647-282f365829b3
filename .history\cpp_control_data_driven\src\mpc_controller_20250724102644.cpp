#include "mpc_controller.h"
#include <iostream>
#include <chrono>
#include <sstream>

MPCController::MPCController()
    : nx_(0), nu_(1), ny_(1), nd_(0), Ts_(5.0),
      predictionHorizon_(60), controlHorizon_(30),
      outputWeight_(100.0), inputWeight_(0.01), inputRateWeight_(1.0),
      inputMin_(0.0), inputMax_(100.0),
      inputRateMin_(-10.0), inputRateMax_(10.0),
      outputMin_(-std::numeric_limits<double>::infinity()),
      outputMax_(std::numeric_limits<double>::infinity()) {
}

MPCController::~MPCController() {
    // 不再需要清理qpSolver_，使用局部对象
}

void MPCController::setModel(const StateSpaceModel& model) {
    model_ = model;
    nx_ = static_cast<int>(model_.getStateSize());
    nu_ = 1;  // 控制输入数量（功率）
    ny_ = static_cast<int>(model_.getOutputSize());
    nd_ = static_cast<int>(model_.getInputSize()) - nu_;  // 测量扰动数量（其他输入）
}

void MPCController::setPredictionHorizon(int horizon) {
    predictionHorizon_ = horizon;
}

void MPCController::setControlHorizon(int horizon) {
    controlHorizon_ = horizon;
}

void MPCController::setSamplingTime(double Ts) {
    Ts_ = Ts;
}

void MPCController::setOutputWeight(double weight) {
    outputWeight_ = weight;
}

void MPCController::setInputWeight(double weight) {
    inputWeight_ = weight;
}

void MPCController::setInputRateWeight(double weight) {
    inputRateWeight_ = weight;
}

void MPCController::setInputConstraints(double min, double max) {
    inputMin_ = min;
    inputMax_ = max;
}

void MPCController::setInputRateConstraints(double min, double max) {
    inputRateMin_ = min;
    inputRateMax_ = max;
}

void MPCController::setOutputConstraints(double min, double max) {
    outputMin_ = min;
    outputMax_ = max;
}

void MPCController::setReference(const Eigen::VectorXd& reference) {
    reference_ = reference;
}

void MPCController::setMeasuredDisturbances(const Eigen::MatrixXd& disturbances) {
    disturbances_ = disturbances;
}

Eigen::VectorXd MPCController::step(const Eigen::VectorXd& currentState, const Eigen::VectorXd& currentOutput) {
    if (reference_.size() < predictionHorizon_) {
        std::cerr << "Reference trajectory length insufficient" << std::endl;
        return Eigen::VectorXd::Zero(nu_);
    }
    
    if (disturbances_.rows() < predictionHorizon_ || disturbances_.cols() != nd_) {
        std::cerr << "Measured disturbance size mismatch" << std::endl;
        return Eigen::VectorXd::Zero(nu_);
    }
    
    // 构建QP问题矩阵
    Eigen::MatrixXd H;
    Eigen::VectorXd g;
    Eigen::MatrixXd A_constraint;
    Eigen::VectorXd lb, ub;
    
    buildQPMatrices(currentState, H, g, A_constraint, lb, ub);
    
    // 求解QP问题
    Eigen::VectorXd solution;
    if (!solveQP(H, g, A_constraint, lb, ub, solution)) {
        std::cerr << "QP solving failed" << std::endl;
        return Eigen::VectorXd::Zero(nu_);
    }
    
    // 提取第一个控制输入
    Eigen::VectorXd u = solution.head(nu_);
    
    // 更新上一步控制输入
    lastInput_ = u;
    
    // 存储优化结果
    optimalInputs_ = solution;
    
    // 计算预测轨迹（未实现）
    // TODO: 实现优化轨迹计算
    
    return u;
}

const Eigen::VectorXd& MPCController::getOptimalInputs() const {
    return optimalInputs_;
}

const Eigen::VectorXd& MPCController::getOptimalOutputs() const {
    return optimalOutputs_;
}

const Eigen::VectorXd& MPCController::getOptimalStates() const {
    return optimalStates_;
}

int MPCController::getQPStatus() const {
    return qpStatus_;
}

void MPCController::buildQPMatrices(const Eigen::VectorXd& currentState,
                                  Eigen::MatrixXd& H,
                                  Eigen::VectorXd& g,
                                  Eigen::MatrixXd& A_constraint,
                                  Eigen::VectorXd& lb,
                                  Eigen::VectorXd& ub) {
    // 获取系统矩阵
    const Eigen::MatrixXd& A = model_.getA();
    const Eigen::MatrixXd& B = model_.getB();
    const Eigen::MatrixXd& C = model_.getC();
    
    // 分离控制输入和测量扰动矩阵
    Eigen::MatrixXd Bu = B.leftCols(nu_);
    Eigen::MatrixXd Bd = B.rightCols(nd_);
    
    // 控制变量数量（每个时间步一个控制输入）
    int nDV = controlHorizon_ * nu_;
    
    // 构建预测矩阵
    // 状态预测矩阵
    Eigen::MatrixXd Sx = Eigen::MatrixXd::Zero(nx_ * predictionHorizon_, nDV);
    // 输出预测矩阵
    Eigen::MatrixXd Sy = Eigen::MatrixXd::Zero(ny_ * predictionHorizon_, nDV);
    // 自由响应（无控制输入时的响应）
    Eigen::VectorXd Fx = Eigen::VectorXd::Zero(nx_ * predictionHorizon_);
    Eigen::VectorXd Fy = Eigen::VectorXd::Zero(ny_ * predictionHorizon_);
    
    // 计算自由响应
    Eigen::VectorXd x = currentState;
    for (int i = 0; i < predictionHorizon_; ++i) {
        // 添加扰动影响
        if (i < disturbances_.rows()) {
            x = A * x + Bd * disturbances_.row(i).transpose();
        } else {
            x = A * x;
        }
        Fx.segment(i * nx_, nx_) = x;
        Fy.segment(i * ny_, ny_) = C * x;
    }
    
    // 计算预测矩阵
    Eigen::MatrixXd Ap = A;
    for (int i = 0; i < predictionHorizon_; ++i) {
        for (int j = 0; j <= std::min(i, controlHorizon_ - 1); ++j) {
            if (i == j) {
                Sx.block(i * nx_, j * nu_, nx_, nu_) = Bu;
            } else {
                Eigen::MatrixXd Ap_pow = A;
                for (int k = 0; k < i - j; ++k) {
                    Ap_pow = Ap_pow * A;
                }
                Sx.block(i * nx_, j * nu_, nx_, nu_) = Ap_pow * Bu;
            }
        }
    }
    
    // 输出预测矩阵 Sy = C * Sx
    for (int i = 0; i < predictionHorizon_; ++i) {
        Sy.block(i * ny_, 0, ny_, nDV) = C * Sx.block(i * nx_, 0, nx_, nDV);
    }
    
    // 构建二次规划问题
    // H = Sy' * Q * Sy + R
    H = Sy.transpose() * outputWeight_ * Eigen::MatrixXd::Identity(ny_ * predictionHorizon_, ny_ * predictionHorizon_) * Sy +
        inputWeight_ * Eigen::MatrixXd::Identity(nDV, nDV);
    
    // 添加控制率权重
    if (inputRateWeight_ > 0) {
        Eigen::MatrixXd R_delta = Eigen::MatrixXd::Zero(nDV, nDV);
        for (int i = 0; i < controlHorizon_ - 1; ++i) {
            R_delta(i, i) += inputRateWeight_;
            R_delta(i + 1, i + 1) += inputRateWeight_;
            R_delta(i, i + 1) = -inputRateWeight_;
            R_delta(i + 1, i) = -inputRateWeight_;
        }
        H += R_delta;
    }
    
    // g = -Sy' * Q * (Yref - Fy)
    Eigen::VectorXd Yref = Eigen::VectorXd::Zero(ny_ * predictionHorizon_);
    for (int i = 0; i < predictionHorizon_; ++i) {
        Yref.segment(i * ny_, ny_) = reference_.segment(i * ny_, ny_);
    }
    g = -Sy.transpose() * outputWeight_ * Eigen::MatrixXd::Identity(ny_ * predictionHorizon_, ny_ * predictionHorizon_) * (Yref - Fy);
    
    // 添加输入变化项
    if (lastInput_.size() > 0 && inputRateWeight_ > 0) {
        Eigen::VectorXd u0 = Eigen::VectorXd::Zero(nDV);
        u0.head(nu_) = lastInput_;
        g += inputRateWeight_ * u0;
    }
    
    // 构建约束矩阵
    // 输入约束
    Eigen::MatrixXd A_u = Eigen::MatrixXd::Identity(nDV, nDV);
    Eigen::VectorXd lb_u = Eigen::VectorXd::Constant(nDV, inputMin_);
    Eigen::VectorXd ub_u = Eigen::VectorXd::Constant(nDV, inputMax_);
    
    // 输入变化约束
    Eigen::MatrixXd A_du = Eigen::MatrixXd::Zero(nDV, nDV);
    Eigen::VectorXd lb_du = Eigen::VectorXd::Constant(nDV, inputRateMin_);
    Eigen::VectorXd ub_du = Eigen::VectorXd::Constant(nDV, inputRateMax_);
    
    if (lastInput_.size() > 0) {
        A_du.block(0, 0, nu_, nu_) = Eigen::MatrixXd::Identity(nu_, nu_);
        lb_du.head(nu_) = lastInput_ + Eigen::VectorXd::Constant(nu_, inputRateMin_);
        ub_du.head(nu_) = lastInput_ + Eigen::VectorXd::Constant(nu_, inputRateMax_);
        
        for (int i = 1; i < controlHorizon_; ++i) {
            A_du.block(i * nu_, i * nu_, nu_, nu_) = Eigen::MatrixXd::Identity(nu_, nu_);
            A_du.block(i * nu_, (i - 1) * nu_, nu_, nu_) = -Eigen::MatrixXd::Identity(nu_, nu_);
        }
    }
    
    // 输出约束
    Eigen::MatrixXd A_y = Sy;
    Eigen::VectorXd lb_y = Eigen::VectorXd::Constant(ny_ * predictionHorizon_, outputMin_) - Fy;
    Eigen::VectorXd ub_y = Eigen::VectorXd::Constant(ny_ * predictionHorizon_, outputMax_) - Fy;
    
    // 合并约束
    // 这里仅使用输入约束和输入变化约束
    A_constraint = Eigen::MatrixXd::Zero(2 * nDV, nDV);
    A_constraint << A_u, A_du;
    
    lb = Eigen::VectorXd::Zero(2 * nDV);
    lb << lb_u, lb_du;
    
    ub = Eigen::VectorXd::Zero(2 * nDV);
    ub << ub_u, ub_du;
}

bool MPCController::solveQP(const Eigen::MatrixXd& H,
                          const Eigen::VectorXd& g,
                          const Eigen::MatrixXd& A_constraint,
                          const Eigen::VectorXd& lb,
                          const Eigen::VectorXd& ub,
                          Eigen::VectorXd& solution) {

    // 仿照MATLAB的简单调用方式，使用QProblemB（只有边界约束）
    // 这样更高效，因为我们的MPC问题主要是边界约束

    int nDV = H.rows(); // 决策变量数量

    // 使用QProblemB而不是SQProblem，因为我们主要处理边界约束
    qpOASES::QProblemB qp(nDV);

    // 设置选项，仿照MATLAB中的设置
    qpOASES::Options options;
    options.setToMPC();  // 使用MPC专用设置
    options.printLevel = qpOASES::PL_NONE;  // 完全不打印信息
    options.enableCholeskyRefactorisation = 1;  // 启用Cholesky重分解
    options.numRefinementSteps = 1;  // 细化步数
    options.enableFlippingBounds = qpOASES::BT_FALSE;  // 禁用边界翻转（加速）

    // 使用qpOASES的全局消息处理器来禁用所有输出
    qpOASES::getGlobalMessageHandler()->setErrorVisibilityStatus(qpOASES::VS_HIDDEN);
    qpOASES::getGlobalMessageHandler()->setWarningVisibilityStatus(qpOASES::VS_HIDDEN);
    qpOASES::getGlobalMessageHandler()->setInfoVisibilityStatus(qpOASES::VS_HIDDEN);

    qp.setOptions(options);
    
    // 准备数据，仿照MATLAB的简单调用方式
    // 只使用边界约束，不使用一般约束矩阵（更高效）

    // 转换Hessian矩阵 (H必须是对称正定的)
    qpOASES::real_t* H_data = new qpOASES::real_t[nDV * nDV];
    for (int i = 0; i < nDV; ++i) {
        for (int j = 0; j < nDV; ++j) {
            H_data[i * nDV + j] = H(i, j);
        }
    }

    // 转换梯度向量
    qpOASES::real_t* g_data = new qpOASES::real_t[nDV];
    for (int i = 0; i < nDV; ++i) {
        g_data[i] = g(i);
    }

    // 转换变量边界约束（这是QProblemB的主要约束）
    qpOASES::real_t* lb_data = new qpOASES::real_t[nDV];
    qpOASES::real_t* ub_data = new qpOASES::real_t[nDV];
    for (int i = 0; i < nDV; ++i) {
        lb_data[i] = lb(i);
        ub_data[i] = ub(i);
    }
    
    // 求解QP问题，仿照MATLAB的简单调用方式
    // 使用QProblemB的init方法，只处理边界约束
    int nWSR = 100; // 最大迭代次数
    qpOASES::returnValue ret;

    // 记录求解时间
    auto start_time = std::chrono::high_resolution_clock::now();

    // 重定向stdout来禁用qpOASES求解时的输出
    std::streambuf* orig2 = std::cout.rdbuf();
    std::ostringstream devnull2;
    std::cout.rdbuf(devnull2.rdbuf());

    // 直接求解，类似MATLAB中的 qpOASES(H, f, [], [], [], lb, ub, options)
    ret = qp.init(H_data, g_data, lb_data, ub_data, nWSR);

    // 恢复stdout
    std::cout.rdbuf(orig2);

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);

    // 输出求解时间（每10次输出一次）
    static int solve_count = 0;
    static double total_time = 0.0;
    solve_count++;
    total_time += duration.count() / 1000.0;

    if (solve_count % 200 == 0) {
        std::cout << "QP solve #" << solve_count << ": " << duration.count() / 1000.0 << " ms, "
                  << "avg: " << total_time / solve_count << " ms, "
                  << "iterations: " << nWSR << ", status: " << ret << std::endl;
    }

    // 检查求解结果
    bool success = (ret == qpOASES::SUCCESSFUL_RETURN);
    qpStatus_ = ret;

    if (success) {
        // 获取解
        solution.resize(nDV);
        qpOASES::real_t* x_opt = new qpOASES::real_t[nDV];
        qp.getPrimalSolution(x_opt);

        for (int i = 0; i < nDV; ++i) {
            solution(i) = x_opt[i];
        }

        delete[] x_opt;
    } else {
        // 如果求解失败，输出错误信息
        std::cerr << "QP solver failed with return value: " << ret << std::endl;
        solution = Eigen::VectorXd::Zero(nDV);
    }

    // 清理内存
    delete[] H_data;
    delete[] g_data;
    delete[] lb_data;
    delete[] ub_data;

    return success;
} 