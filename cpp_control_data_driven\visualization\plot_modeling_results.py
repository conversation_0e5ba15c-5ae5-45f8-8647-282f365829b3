#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型识别结果可视化脚本
用于生成与MATLAB版本相同的结果图表
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import json
import argparse
import os
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_model_data(model_dir):
    """加载模型数据"""
    # 加载模型信息
    with open(os.path.join(model_dir, 'model_info.json'), 'r', encoding='utf-8') as f:
        model_info = json.load(f)
    
    # 加载矩阵
    A = np.loadtxt(os.path.join(model_dir, 'A_matrix.csv'), delimiter=',')
    B = np.loadtxt(os.path.join(model_dir, 'B_matrix.csv'), delimiter=',')
    C = np.loadtxt(os.path.join(model_dir, 'C_matrix.csv'), delimiter=',')
    
    return A, B, C, model_info

def predict_exit_temp(A, B, U_test, X_test):
    """预测出口温度"""
    N_samples = U_test.shape[0]
    
    # 提取A和B矩阵的最后一行（对应出口温度的预测）
    A_exit = A[-1, :]
    B_exit = B[-1, :]
    
    Y_pred = np.zeros(N_samples - 1)
    
    for k in range(N_samples - 1):
        # 使用识别的系统动态: x(k+1) = A*x(k) + B*u(k)
        # 只关注最后一行（出口温度）方程
        next_exit_temp = np.dot(A_exit, X_test[k, :]) + np.dot(B_exit, U_test[k, :])
        Y_pred[k] = next_exit_temp
    
    return Y_pred

def plot_modeling_results(data_file, model_dir, output_dir='./fig'):
    """绘制模型识别结果"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载数据
    print("Loading data...")
    data = pd.read_csv(data_file)
    
    # 加载模型
    print("Loading model...")
    A, B, C, model_info = load_model_data(model_dir)
    
    # 获取变量信息
    state_cols = model_info['state_variables']
    input_cols = model_info['input_variables']
    
    # 数据分割（90%训练，10%测试）
    train_ratio = 0.9
    total_samples = len(data)
    train_num = int(total_samples * train_ratio)
    
    # 提取测试数据
    test_data = data.iloc[train_num:].reset_index(drop=True)
    
    # 准备状态和输入数据
    X_test = test_data[state_cols].values
    U_test = test_data[input_cols].values
    Y_test = test_data['measureExitTemp'].values
    
    # 预测出口温度
    print("Predicting exit temperature...")
    Y_pred = predict_exit_temp(A, B, U_test, X_test)
    Y_test_adjusted = Y_test[:-1]  # 移除最后一个样本
    
    # 计算误差
    error = Y_test_adjusted - Y_pred
    rmse = np.sqrt(np.mean(error**2))
    print(f"Test RMSE: {rmse:.4f}")
    
    # 计算时间轴（从样本索引转换为小时）
    time_h = np.arange(len(Y_test_adjusted)) * 5 / 3600  # 每个样本间隔5秒，转换为小时
    
    # 创建图表
    fig = plt.figure(figsize=(15, 12))
    fig.suptitle('State-Space Model Identification Results', fontsize=16, fontweight='bold')
    
    # 科学配色方案
    colors = ['#0072BD', '#D95319', '#EDB120', '#7E2F8E', '#77AC30', '#4DBEEE', '#A2142F']
    
    # 1. 预测结果与误差
    ax1 = plt.subplot(3, 2, 1)
    ax1_twin = ax1.twinx()
    
    # 左轴：温度
    line1 = ax1.plot(time_h, Y_test_adjusted, linewidth=2, color=colors[0], label='Measured Output')
    line2 = ax1.plot(time_h, Y_pred, '--', linewidth=2, color=colors[1], label='Predicted Output')
    ax1.set_ylabel('Temperature (°C)', color='black')
    ax1.set_ylim([700, 840])
    
    # 右轴：误差
    line3 = ax1_twin.plot(time_h, error, linewidth=1.5, color=colors[6], alpha=0.7)
    ax1_twin.set_ylabel('Error (°C)', color=colors[6])
    ax1_twin.axhline(y=0, color='k', linestyle='--', linewidth=2, alpha=0.5)
    ax1_twin.set_ylim([min(error)*1.2, 4*max(error)])
    
    ax1.set_xlabel('Time (h)')
    ax1.set_title('RTF Exit Temperature Prediction and Error')
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # 2. 管温度分布（彩色图）
    ax2 = plt.subplot(3, 2, 2)
    
    # 获取管温度数据
    tube_temp_cols = [col for col in state_cols if col.startswith('tt')]
    tube_temps = test_data[tube_temp_cols].iloc[:len(Y_test_adjusted)].values
    
    # 使用jet colormap
    cmap = plt.cm.jet
    colors_tube = cmap(np.linspace(0, 1, len(tube_temp_cols)))
    
    for i, temp_col in enumerate(tube_temp_cols):
        ax2.plot(time_h, tube_temps[:, i], linewidth=1, color=colors_tube[i], alpha=0.8)
    
    # 添加colorbar
    sm = plt.cm.ScalarMappable(cmap=cmap, norm=plt.Normalize(vmin=1, vmax=len(tube_temp_cols)))
    sm.set_array([])
    cbar = plt.colorbar(sm, ax=ax2)
    cbar.set_label('Tube Position')
    
    ax2.set_xlabel('Time (h)')
    ax2.set_ylabel('Temperature (°C)')
    ax2.set_title('Tube Temperatures')
    ax2.grid(True, alpha=0.3)
    
    # 3. 平均功率
    ax3 = plt.subplot(3, 2, 3)
    power_data = test_data['avg_power'].iloc[:len(Y_test_adjusted)].values
    ax3.plot(time_h, power_data, linewidth=2, color=colors[4])
    ax3.set_xlabel('Time (h)')
    ax3.set_ylabel('Value (%)')
    ax3.set_title('Tube Power')
    ax3.grid(True, alpha=0.3)
    
    # 4. 入口温度和速度（双轴）
    ax4 = plt.subplot(3, 2, 4)
    ax4_twin = ax4.twinx()
    
    entry_temp = test_data['measureEntryTemp'].iloc[:len(Y_test_adjusted)].values
    speed = test_data['speed'].iloc[:len(Y_test_adjusted)].values
    
    line1 = ax4.plot(time_h, entry_temp, linewidth=2, color=colors[0], label='Entry Temperature')
    ax4.set_ylabel('Entry Temperature (°C)', color=colors[0])
    
    line2 = ax4_twin.plot(time_h, speed, linewidth=2, color=colors[1], label='Speed')
    ax4_twin.set_ylabel('Speed (m/min)', color=colors[1])
    
    ax4.set_xlabel('Time (h)')
    ax4.set_title('Entry Temperature and Speed')
    ax4.grid(True, alpha=0.3)
    
    # 合并图例
    lines1, labels1 = ax4.get_legend_handles_labels()
    lines2, labels2 = ax4_twin.get_legend_handles_labels()
    ax4.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    # 5. 材料1属性（厚度和宽度）
    ax5 = plt.subplot(3, 2, 5)
    ax5_twin = ax5.twinx()
    
    thick1 = test_data['thick1'].iloc[:len(Y_test_adjusted)].values
    width1 = test_data['width1'].iloc[:len(Y_test_adjusted)].values
    
    line1 = ax5.plot(time_h, thick1, linewidth=2, color=colors[0], label='Thickness 1')
    ax5.set_ylabel('Thickness 1 (μm)', color=colors[0])
    
    line2 = ax5_twin.plot(time_h, width1, linewidth=2, color=colors[1], label='Width 1')
    ax5_twin.set_ylabel('Width 1 (mm)', color=colors[1])
    
    ax5.set_xlabel('Time (h)')
    ax5.set_title('Material 1 Properties')
    ax5.grid(True, alpha=0.3)
    
    # 合并图例
    lines1, labels1 = ax5.get_legend_handles_labels()
    lines2, labels2 = ax5_twin.get_legend_handles_labels()
    ax5.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    # 6. 材料2属性（厚度和宽度）
    ax6 = plt.subplot(3, 2, 6)
    ax6_twin = ax6.twinx()
    
    thick2 = test_data['thick2'].iloc[:len(Y_test_adjusted)].values
    width2 = test_data['width2'].iloc[:len(Y_test_adjusted)].values
    
    line1 = ax6.plot(time_h, thick2, linewidth=2, color=colors[0], label='Thickness 2')
    ax6.set_ylabel('Thickness 2 (μm)', color=colors[0])
    
    line2 = ax6_twin.plot(time_h, width2, linewidth=2, color=colors[1], label='Width 2')
    ax6_twin.set_ylabel('Width 2 (mm)', color=colors[1])
    
    ax6.set_xlabel('Time (h)')
    ax6.set_title('Material 2 Properties')
    ax6.grid(True, alpha=0.3)
    
    # 合并图例
    lines1, labels1 = ax6.get_legend_handles_labels()
    lines2, labels2 = ax6_twin.get_legend_handles_labels()
    ax6.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.tight_layout()
    
    # 保存图片
    output_file = os.path.join(output_dir, 'CPP_ss_AB_id_results.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Results plot saved to: {output_file}")
    
    plt.show()
    
    return rmse

def main():
    parser = argparse.ArgumentParser(description='Plot model identification results')
    parser.add_argument('--data', required=True, help='Path to data CSV file')
    parser.add_argument('--model', required=True, help='Path to model directory')
    parser.add_argument('--output', default='./fig', help='Output directory for plots')
    
    args = parser.parse_args()
    
    rmse = plot_modeling_results(args.data, args.model, args.output)
    print(f"Final RMSE: {rmse:.4f}")

if __name__ == "__main__":
    main()
